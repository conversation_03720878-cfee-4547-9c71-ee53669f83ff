"""
Test script for the enhanced coordinate system functionality.
This script can be run from within Blender to test the coordinate transformations.
"""

import mathutils
import sys
import os

# Add the io_ogre directory to the path so we can import our modules
addon_dir = os.path.dirname(__file__)
if addon_dir not in sys.path:
    sys.path.append(addon_dir)

try:
    from coordinate_system import CoordinateSystem, get_coordinate_system, validate_axis_mapping, get_transformation_preview
    from coordinate_system import COORDINATE_SYSTEMS
    import config
    
    def test_coordinate_system_creation():
        """Test creating coordinate systems."""
        print("Testing coordinate system creation...")
        
        # Test valid coordinate systems
        try:
            cs1 = CoordinateSystem(forward='y', up='z', right='x')
            print(f"✓ Created Blender default: {cs1}")
            
            cs2 = CoordinateSystem(forward='z', up='-y', right='x')
            print(f"✓ Created OGRE standard: {cs2}")
            
            cs3 = CoordinateSystem(forward='z', up='y', right='x')
            print(f"✓ Created Unity standard: {cs3}")
            
        except Exception as e:
            print(f"✗ Error creating coordinate system: {e}")
            return False
        
        # Test invalid coordinate systems
        try:
            cs_invalid = CoordinateSystem(forward='x', up='x', right='y')  # Duplicate axis
            print("✗ Should have failed with duplicate axis")
            return False
        except Exception:
            print("✓ Correctly rejected duplicate axis")
        
        return True
    
    def test_vector_transformations():
        """Test vector transformations."""
        print("\nTesting vector transformations...")
        
        try:
            # Test with OGRE standard coordinate system
            ogre_cs = COORDINATE_SYSTEMS['ogre_standard']
            
            # Test basic vectors
            test_vectors = [
                mathutils.Vector((1, 0, 0)),  # Right
                mathutils.Vector((0, 1, 0)),  # Forward in Blender
                mathutils.Vector((0, 0, 1)),  # Up in Blender
            ]
            
            for i, vec in enumerate(test_vectors):
                transformed = ogre_cs.transform_vector(vec)
                print(f"  Vector {vec} -> {transformed}")
            
            # Test quaternion transformation
            quat = mathutils.Quaternion((1, 0, 0, 0))  # Identity quaternion
            transformed_quat = ogre_cs.transform_quaternion(quat)
            print(f"  Quaternion {quat} -> {transformed_quat}")
            
            return True
            
        except Exception as e:
            print(f"✗ Error in vector transformation: {e}")
            return False
    
    def test_validation():
        """Test axis mapping validation."""
        print("\nTesting axis mapping validation...")
        
        # Test valid mappings
        valid_cases = [
            ('x', 'y', 'z'),
            ('y', 'z', 'x'),
            ('-x', 'y', 'z'),
            ('x', '-y', 'z'),
        ]
        
        for forward, up, right in valid_cases:
            is_valid, error = validate_axis_mapping(forward, up, right)
            if is_valid:
                print(f"✓ Valid: forward={forward}, up={up}, right={right}")
            else:
                print(f"✗ Should be valid: forward={forward}, up={up}, right={right} - {error}")
                return False
        
        # Test invalid mappings
        invalid_cases = [
            ('x', 'x', 'y'),  # Duplicate axis
            ('x', 'y', 'y'),  # Duplicate axis
            ('x', 'y', 'w'),  # Invalid axis
        ]
        
        for forward, up, right in invalid_cases:
            is_valid, error = validate_axis_mapping(forward, up, right)
            if not is_valid:
                print(f"✓ Correctly invalid: forward={forward}, up={up}, right={right} - {error}")
            else:
                print(f"✗ Should be invalid: forward={forward}, up={up}, right={right}")
                return False
        
        return True
    
    def test_presets():
        """Test coordinate system presets."""
        print("\nTesting coordinate system presets...")
        
        try:
            for preset_name in COORDINATE_SYSTEMS.keys():
                cs = get_coordinate_system(preset_name)
                print(f"✓ Loaded preset '{preset_name}': {cs}")
            
            # Test custom coordinate system
            config.CONFIG = {
                'COORDINATE_SYSTEM_PRESET': 'custom',
                'CUSTOM_AXIS_FORWARD': 'z',
                'CUSTOM_AXIS_UP': 'y',
                'CUSTOM_AXIS_RIGHT': 'x'
            }
            
            custom_cs = get_coordinate_system('custom')
            print(f"✓ Created custom coordinate system: {custom_cs}")
            
            return True
            
        except Exception as e:
            print(f"✗ Error testing presets: {e}")
            return False
    
    def test_transformation_preview():
        """Test transformation preview functionality."""
        print("\nTesting transformation preview...")
        
        try:
            blender_cs = COORDINATE_SYSTEMS['blender_default']
            ogre_cs = COORDINATE_SYSTEMS['ogre_standard']
            
            preview = get_transformation_preview(blender_cs, ogre_cs)
            
            print("Transformation preview (Blender -> OGRE):")
            for axis_name, data in preview.items():
                print(f"  {axis_name}: {data['original']} -> {data['transformed']}")
            
            return True
            
        except Exception as e:
            print(f"✗ Error in transformation preview: {e}")
            return False
    
    def run_all_tests():
        """Run all coordinate system tests."""
        print("=" * 60)
        print("COORDINATE SYSTEM TESTS")
        print("=" * 60)
        
        tests = [
            test_coordinate_system_creation,
            test_vector_transformations,
            test_validation,
            test_presets,
            test_transformation_preview,
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed += 1
                else:
                    print(f"✗ Test {test.__name__} failed")
            except Exception as e:
                print(f"✗ Test {test.__name__} crashed: {e}")
        
        print("\n" + "=" * 60)
        print(f"RESULTS: {passed}/{total} tests passed")
        print("=" * 60)
        
        return passed == total
    
    if __name__ == "__main__":
        success = run_all_tests()
        if not success:
            print("Some tests failed!")
        else:
            print("All tests passed!")

except ImportError as e:
    print(f"Could not import coordinate system modules: {e}")
    print("Make sure you're running this from within Blender with the addon loaded.")
except Exception as e:
    print(f"Unexpected error: {e}")
