{\rtf1\ansi\ansicpg1252\deff0{\fonttbl{\f0\fnil\fcharset0 Calibri;}}
{\colortbl ;\red0\green128\blue0;}
{\*\generator Msftedit 5.41.21.2510;}\viewkind4\uc1\pard\sa200\sl276\slmult1\cf1\lang11\b\f0\fs28 blender2ogre\cf0\fs22  \b0 is a Blender exporter addon that enables you to export your scene as Ogre assets. In addition you can export as a realXtend Tundra scene, the assets will still be Ogre assets as Tundras rendering engine is Ogre.\par
\b\fs24 Ogre\b0\fs28  \fs22 export generates .scene, .mesh, .skeleton and .material files. Ogre spesific tweaks can be done via the various UI panels in Blender that this addon provides. \b\fs24 realXtend Tundra\b0\fs28  \fs22 export generates a .txml scene description with the needed Ogre assets.\fs24\line\fs22\par
}
 