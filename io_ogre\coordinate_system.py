"""
Enhanced coordinate system transformation module for blender2ogre.

This module provides comprehensive coordinate system transformation capabilities,
including predefined presets and custom axis mapping support.
"""

import mathutils
import logging
from . import config

logger = logging.getLogger('coordinate_system')

class CoordinateSystemError(Exception):
    """Exception raised for coordinate system related errors."""
    pass

class CoordinateSystem:
    """
    Represents a coordinate system with forward, up, and right axis definitions.
    Provides transformation matrices and validation.
    """
    
    def __init__(self, forward='y', up='z', right='x'):
        """
        Initialize coordinate system.
        
        Args:
            forward: Forward axis direction ('+x', '-x', '+y', '-y', '+z', '-z')
            up: Up axis direction 
            right: Right axis direction
        """
        self.forward = self._normalize_axis(forward)
        self.up = self._normalize_axis(up)
        self.right = self._normalize_axis(right)
        
        self._validate_axes()
        self._compute_transformation_matrix()
    
    def _normalize_axis(self, axis):
        """Normalize axis string to standard format."""
        axis = str(axis).lower().strip()
        if axis in ['x', '+x']: return 'x'
        elif axis == '-x': return '-x'
        elif axis in ['y', '+y']: return 'y'
        elif axis == '-y': return '-y'
        elif axis in ['z', '+z']: return 'z'
        elif axis == '-z': return '-z'
        else:
            raise CoordinateSystemError(f"Invalid axis specification: {axis}")
    
    def _validate_axes(self):
        """Validate that the three axes form a valid coordinate system."""
        axes = [self.forward, self.up, self.right]
        
        # Check for duplicate axes
        axis_chars = [abs_axis.replace('-', '') for abs_axis in axes]
        if len(set(axis_chars)) != 3:
            raise CoordinateSystemError(
                f"Axes must be orthogonal: forward={self.forward}, up={self.up}, right={self.right}")
        
        # Check that we have x, y, z
        if set(axis_chars) != {'x', 'y', 'z'}:
            raise CoordinateSystemError(
                f"Must specify all three axes (x,y,z): forward={self.forward}, up={self.up}, right={self.right}")
    
    def _compute_transformation_matrix(self):
        """Compute the transformation matrix for this coordinate system."""
        # Create basis vectors for the coordinate system
        forward_vec = self._axis_to_vector(self.forward)
        up_vec = self._axis_to_vector(self.up)
        right_vec = self._axis_to_vector(self.right)
        
        # Create transformation matrix
        # Each column represents where the standard basis vectors (x,y,z) map to
        self.matrix = mathutils.Matrix([
            [right_vec.x, up_vec.x, forward_vec.x, 0],
            [right_vec.y, up_vec.y, forward_vec.y, 0],
            [right_vec.z, up_vec.z, forward_vec.z, 0],
            [0, 0, 0, 1]
        ])
        
        self.matrix_3x3 = self.matrix.to_3x3()
        self.inverse_matrix = self.matrix.inverted()
        self.inverse_matrix_3x3 = self.inverse_matrix.to_3x3()
    
    def _axis_to_vector(self, axis):
        """Convert axis string to vector."""
        if axis == 'x': return mathutils.Vector((1, 0, 0))
        elif axis == '-x': return mathutils.Vector((-1, 0, 0))
        elif axis == 'y': return mathutils.Vector((0, 1, 0))
        elif axis == '-y': return mathutils.Vector((0, -1, 0))
        elif axis == 'z': return mathutils.Vector((0, 0, 1))
        elif axis == '-z': return mathutils.Vector((0, 0, -1))
        else:
            raise CoordinateSystemError(f"Invalid axis: {axis}")
    
    def transform_vector(self, vector):
        """Transform a 3D vector to this coordinate system."""
        if len(vector) == 3:
            return self.matrix_3x3 @ mathutils.Vector(vector)
        else:
            raise CoordinateSystemError(f"Expected 3D vector, got {len(vector)}D")
    
    def transform_quaternion(self, quaternion):
        """Transform a quaternion to this coordinate system."""
        if len(quaternion) == 4:
            # Convert quaternion to matrix, transform, then back to quaternion
            quat_matrix = quaternion.to_matrix()
            transformed_matrix = self.matrix_3x3 @ quat_matrix @ self.inverse_matrix_3x3
            return transformed_matrix.to_quaternion()
        else:
            raise CoordinateSystemError(f"Expected quaternion (4D), got {len(quaternion)}D")
    
    def __str__(self):
        return f"CoordinateSystem(forward={self.forward}, up={self.up}, right={self.right})"

# Predefined coordinate systems
COORDINATE_SYSTEMS = {
    'blender_default': CoordinateSystem(forward='y', up='z', right='x'),
    'ogre_standard': CoordinateSystem(forward='z', up='-y', right='x'),  # This maps to xz-y transform
    'unity_standard': CoordinateSystem(forward='z', up='y', right='x'),
    'unreal_standard': CoordinateSystem(forward='x', up='z', right='y'),
    'maya_standard': CoordinateSystem(forward='z', up='y', right='x'),
    '3dsmax_standard': CoordinateSystem(forward='y', up='z', right='x'),
}

def get_coordinate_system(preset_name=None):
    """
    Get coordinate system based on preset name or current configuration.
    
    Args:
        preset_name: Name of preset, or None to use current config
        
    Returns:
        CoordinateSystem instance
    """
    if preset_name is None:
        preset_name = config.get('COORDINATE_SYSTEM_PRESET', 'ogre_standard')
    
    if preset_name == 'custom':
        forward = config.get('CUSTOM_AXIS_FORWARD', 'y')
        up = config.get('CUSTOM_AXIS_UP', 'z')
        right = config.get('CUSTOM_AXIS_RIGHT', 'x')
        return CoordinateSystem(forward=forward, up=up, right=right)
    elif preset_name in COORDINATE_SYSTEMS:
        return COORDINATE_SYSTEMS[preset_name]
    else:
        logger.warning(f"Unknown coordinate system preset: {preset_name}, using ogre_standard")
        return COORDINATE_SYSTEMS['ogre_standard']

def get_legacy_transformation():
    """
    Get transformation that matches the legacy swap() function behavior.
    This ensures backward compatibility.
    """
    swap_mode = config.get('SWAP_AXIS', 'xz-y')
    
    if swap_mode == 'xyz':
        return CoordinateSystem(forward='y', up='z', right='x')  # No transformation
    elif swap_mode == 'xzy':
        return CoordinateSystem(forward='z', up='y', right='x')
    elif swap_mode == '-xzy':
        return CoordinateSystem(forward='z', up='y', right='-x')
    elif swap_mode == 'xz-y':
        return CoordinateSystem(forward='z', up='-y', right='x')
    else:
        logger.warning(f"Unknown legacy swap mode: {swap_mode}, using xz-y")
        return CoordinateSystem(forward='z', up='-y', right='x')

def validate_axis_mapping(forward, up, right):
    """
    Validate that the given axis mapping is valid.
    
    Returns:
        (is_valid, error_message)
    """
    try:
        CoordinateSystem(forward=forward, up=up, right=right)
        return True, ""
    except CoordinateSystemError as e:
        return False, str(e)

def get_transformation_preview(source_system, target_system):
    """
    Get a preview of how vectors transform between coordinate systems.
    
    Returns:
        Dictionary with transformation examples
    """
    # Test vectors
    test_vectors = {
        'forward': mathutils.Vector((0, 1, 0)),
        'up': mathutils.Vector((0, 0, 1)),
        'right': mathutils.Vector((1, 0, 0)),
    }
    
    preview = {}
    for name, vec in test_vectors.items():
        # Transform from source to target
        if source_system:
            vec_in_world = source_system.inverse_matrix_3x3 @ vec
        else:
            vec_in_world = vec
            
        transformed = target_system.transform_vector(vec_in_world)
        preview[name] = {
            'original': tuple(vec),
            'transformed': tuple(transformed)
        }
    
    return preview
