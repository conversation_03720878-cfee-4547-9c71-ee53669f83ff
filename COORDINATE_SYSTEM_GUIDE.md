# Enhanced Coordinate System Guide for blender2ogre

## Overview

The enhanced blender2ogre addon now includes comprehensive coordinate system transformation capabilities that eliminate the need for manual post-processing when importing Blender models into OGRE-based applications.

## Features

### 1. Predefined Coordinate System Presets

The addon includes several predefined coordinate system presets:

- **Blender Default**: Y-up, Z-forward (standard Blender coordinate system)
- **OGRE Standard**: Z-up, Y-forward (standard OGRE coordinate system)
- **Unity Standard**: Y-up, Z-forward (Unity-compatible)
- **Unreal Standard**: Z-up, X-forward (Unreal Engine coordinate system)
- **Maya Standard**: Y-up, Z-forward (Maya-compatible)
- **3ds Max Standard**: Z-up, Y-forward (3ds Max coordinate system)
- **Custom Mapping**: User-defined axis mapping

### 2. Custom Axis Mapping

When "Custom Mapping" is selected, you can define your own coordinate system by specifying:

- **Forward Axis**: The direction that represents "forward" in your target application
- **Up Axis**: The direction that represents "up" in your target application  
- **Right Axis**: The direction that represents "right" in your target application

Each axis can be set to +X, -X, +Y, -Y, +Z, or -Z.

### 3. Automatic Validation

The system automatically validates your axis mapping to ensure:

- All three axes (X, Y, Z) are represented
- No duplicate axes are used
- The coordinate system forms a valid right-handed or left-handed system

### 4. Comprehensive Transformations

The coordinate system transformations are applied to:

- **Vertex positions**: All mesh vertex coordinates
- **Normals**: Surface normal vectors
- **Tangents**: Tangent vectors for normal mapping
- **Bone transformations**: Armature bone positions and rotations
- **Animations**: Keyframe data for both object and bone animations
- **Camera orientations**: Camera position and rotation
- **Scene node transforms**: All object transforms in the scene

## Usage

### Basic Usage

1. Open the Blender2OGRE export dialog
2. In the "General" section, find the "Coordinate System" dropdown
3. Select your target coordinate system preset
4. Export as normal

### Custom Coordinate System

1. Set "Coordinate System" to "Custom Mapping"
2. Configure the Forward, Up, and Right axes according to your target application
3. Verify that the validation shows "Custom axis mapping is valid"
4. Export as normal

### Legacy Compatibility

The addon maintains full backward compatibility with the legacy axis swapping modes. You can still use the "Legacy Swap Axis" option in the Legacy Options section if needed.

## Technical Details

### Transformation Mathematics

The coordinate system transformations use 4x4 transformation matrices to ensure accurate and consistent transformations across all data types. The system:

1. Computes the transformation matrix based on the target coordinate system
2. Applies the transformation to 3D vectors using matrix multiplication
3. Handles quaternions by converting to matrix form, transforming, and converting back
4. Preserves the mathematical relationships between different vector types

### Performance Considerations

- Transformations are computed once per export and cached
- Matrix operations are optimized using Blender's mathutils library
- No significant performance impact on export times

### Error Handling

The system includes comprehensive error handling:

- Invalid axis mappings are detected and reported
- Fallback to legacy transformation if the new system fails
- Clear error messages guide users to correct configuration issues

## Migration from Legacy System

### Automatic Migration

Existing projects using legacy axis swapping modes will continue to work without changes. The system automatically detects legacy configurations and applies the appropriate transformations.

### Recommended Migration Path

1. **Test with current settings**: Export with your current legacy settings to establish a baseline
2. **Switch to preset**: Try the equivalent preset (e.g., "OGRE Standard" for "xz-y" legacy mode)
3. **Verify results**: Compare the exported models to ensure they match your expectations
4. **Update workflow**: Once verified, update your export workflow to use the new presets

### Legacy Mode Mapping

- `xyz` (no swapping) → "Blender Default"
- `xz-y` (OGRE standard) → "OGRE Standard"  
- `-xzy` (non-standard) → Custom mapping with forward=-X, up=Z, right=Y
- `xzy` → Custom mapping with forward=Z, up=Y, right=X

## Troubleshooting

### Common Issues

**"Invalid axis mapping" error**
- Ensure all three axes (X, Y, Z) are represented
- Check that no axis is used twice
- Verify axis specifications use valid values (+X, -X, +Y, -Y, +Z, -Z)

**Models appear rotated or flipped**
- Verify you've selected the correct target coordinate system
- Check if your target application uses a different handedness
- Try the transformation preview to understand the conversion

**Animations don't work correctly**
- Ensure bone transformations are being exported
- Check that the coordinate system is applied consistently to both mesh and armature data
- Verify keyframe data is being transformed

### Getting Help

If you encounter issues:

1. Check the Blender console for error messages
2. Try the test script (`test_coordinate_system.py`) to verify the system is working
3. Use the legacy axis swapping as a fallback
4. Report issues on the project's GitHub page with:
   - Your coordinate system configuration
   - Source and target application details
   - Sample files demonstrating the issue

## Examples

### Unity Export
```
Coordinate System: Unity Standard
Forward Axis: +Z
Up Axis: +Y  
Right Axis: +X
```

### Unreal Engine Export
```
Coordinate System: Unreal Standard
Forward Axis: +X
Up Axis: +Z
Right Axis: +Y
```

### Custom Game Engine
```
Coordinate System: Custom Mapping
Forward Axis: +Y
Up Axis: +Z
Right Axis: -X
```

This enhanced coordinate system functionality provides a robust, flexible solution for exporting Blender content to any target coordinate system while maintaining full backward compatibility with existing workflows.
