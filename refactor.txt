
The new structure:

+= io_ogre/                                       # python source code
  +- __init__.py                                  # changelog, blender registration and importing
  += ogre/                                        # code specific to ogre, export formats, xml, materials, ...
   +- converter.py                                # OgreXmlConverter wrapper
   +- export.py                                   # general exporting api
   +- material.py
   +- mesh.py
   +- program.py
   +- skeleton.py
  += tundra/                                       # real extend plugin (not working yet)
  +- config.py                                     # configuration loading, saving
  +- util.py                                       # general purpose functions used in many places
  +- report.py                                     # the reporting as a module rather than inlined
  +- xml.py                                        # xml utility
  +- properties.py                                 # add custom types to objects, materials, ...
  +- ...
  += ui/
   +- __init__.py                                  # general purpose ui elements
   +- export.py                                    # export panel to export scene/mesh
   +- helper.py                                    # help/api docu for the plugin
   +- material.py                                  # material specific additions
 += launch/                                        # several launch scripts (windows bat, linux sh, ...)


A more clear separation between realXtend, Jmoneky, ... has been made. As I see this now this plugin
is mainly for exporting to ogre file format, thus all other code should be moved out...

What I have done:

* moved 'general purpuse' code into util.py
* CMesh is not used in the source code. moved to unused.py
* JMonkey moved to unused. There was a comment saying 'todo: remove...'
* The setup of _TXML_ -> _OgreCommonExport_ +-> OgrePlugin
                                            |
                                            +-> realXtend Plugin

is a bit weired, I don't see why there should be a dependency on _TXML_, since it is only used
for realXtend... now it is the following way:

_OgreCommonExport_ +-> OgrePlugin
                   |
                   +-> _TXML_ -> realXtend Plugin

OgrePlugin does not redefine properties of _OgreCommonExport_ (EX_* = TypeProperty(...) )
* different parts of ogre export (image,material,mesh,program,skeleton) got their own modules
* moved the rpython code to the rogre folder
* moved NVIDIA texture tool doc into unused.py
* usage of CONFIG is discouraged and you should use config.get('setting') and config.update(key=value, key1=value2, ...)

This resulted in about ~2200 loc in blender2ogre/__init__.py (from 7730).
I even further reduced it to 2000 loc, and removed some duplicate code.
6.8.14: further stripped down to 1310 loc


Second refactoring round
===

* for all usable ui features the classes has been renamed.
  OP_name - an operator
  MT_name - a menu
  HT_name - a header
